import argparse
import json
import os
import time
from collections import Counter
from datetime import date

from pyspark.sql import SparkSession
from pyspark.sql.functions import (
    col, collect_list, count, size, split, regexp_extract,
    udf, when, isnan, isnull, desc, asc, hash
)
from pyspark.sql.types import StringType, IntegerType, StructType, StructField


def clean_unicode_string(text):
    """清理字符串中的问题Unicode字符，保持可读性"""
    if not isinstance(text, str):
        return text

    # 方法1: 移除或替换代理字符
    # 检测并处理代理字符对
    cleaned = ""
    i = 0
    while i < len(text):
        char = text[i]
        # 检查是否是代理字符
        if 0xD800 <= ord(char) <= 0xDFFF:
            # 这是一个代理字符，尝试处理
            if i + 1 < len(text):
                next_char = text[i + 1]
                if 0xDC00 <= ord(next_char) <= 0xDFFF:
                    # 这是一个完整的代理对，尝试转换
                    try:
                        # 尝试编码测试
                        surrogate_pair = char + next_char
                        surrogate_pair.encode('utf-8')
                        cleaned += surrogate_pair
                        i += 2
                        continue
                    except UnicodeEncodeError:
                        # 代理对有问题，替换为占位符
                        cleaned += "�"  # Unicode替换字符
                        i += 2
                        continue
            # 单独的代理字符，替换为占位符
            cleaned += "�"
            i += 1
        else:
            # 正常字符，直接添加
            try:
                char.encode('utf-8')
                cleaned += char
            except UnicodeEncodeError:
                cleaned += "�"
            i += 1

    return cleaned


def convert_to_json_serializable(item):
    """转换数据为JSON可序列化格式"""
    if isinstance(item, (date,)):
        return str(item)
    elif isinstance(item, dict):
        return {key: convert_to_json_serializable(value) for key, value in item.items()}
    elif isinstance(item, list):
        return [convert_to_json_serializable(sub_item) for sub_item in item]
    elif isinstance(item, str):
        # 清理Unicode问题字符，保持可读性
        return clean_unicode_string(item)
    return item


def get_session_id(sessionid_str):
    """从sessionid字符串中提取session_id"""
    if sessionid_str is None:
        return None
    return sessionid_str.strip().split("#")[0]


def get_timestamp(sessionid_str):
    """从sessionid字符串中提取timestamp"""
    if sessionid_str is None:
        return None
    try:
        parts = sessionid_str.strip().split("#")
        if len(parts) >= 3:
            return int(parts[-2])
        return None
    except (ValueError, IndexError):
        return None


def create_spark_session(app_name="GroupBySessionId"):
    """创建Spark会话，优化分区和shuffle配置"""
    spark = SparkSession.builder \
        .appName(app_name) \
        .config("spark.sql.adaptive.enabled", "true") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true") \
        .config("spark.sql.adaptive.skewJoin.enabled", "true") \
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
        .config("spark.sql.shuffle.partitions", "200") \
        .config("spark.sql.adaptive.advisoryPartitionSizeInBytes", "128MB") \
        .getOrCreate()

    # 设置日志级别
    spark.sparkContext.setLogLevel("WARN")
    return spark


def validate_session_grouping(df_original, df_grouped):
    """验证session分组的正确性"""
    print("验证session分组正确性...")

    # 检查总记录数是否一致
    original_count = df_original.count()
    grouped_total_records = df_grouped.agg({"record_count": "sum"}).collect()[0][0]

    print(f"原始记录数: {original_count}")
    print(f"分组后总记录数: {grouped_total_records}")

    if original_count != grouped_total_records:
        print("警告: 分组前后记录数不一致！可能存在数据丢失")
        return False

    # 检查session数量
    unique_sessions_original = df_original.select("short_sessionid_").distinct().count()
    unique_sessions_grouped = df_grouped.count()

    print(f"原始唯一session数: {unique_sessions_original}")
    print(f"分组后session数: {unique_sessions_grouped}")

    if unique_sessions_original != unique_sessions_grouped:
        print("警告: session数量不一致！")
        return False

    print("✓ session分组验证通过")
    return True


def optimize_partitioning_strategy(df, session_col="short_sessionid_"):
    """优化分区策略，防止数据倾斜"""
    print("分析数据分布，优化分区策略...")

    # 分析session分布
    session_stats = df.groupBy(session_col).count().agg(
        {"count": "min"}, {"count": "max"}, {"count": "avg"}
    ).collect()[0]

    min_records = session_stats[0]
    max_records = session_stats[1]
    avg_records = session_stats[2]

    print(f"Session记录数统计 - 最小: {min_records}, 最大: {max_records}, 平均: {avg_records:.1f}")

    # 检测数据倾斜
    if max_records > avg_records * 10:
        print("⚠️  检测到数据倾斜，将使用特殊处理策略")
        return True

    return False


def process_with_skew_handling(df, session_col="short_sessionid_"):
    """处理数据倾斜的特殊策略"""
    print("使用数据倾斜处理策略...")

    # 识别大session（记录数超过阈值的session）
    session_sizes = df.groupBy(session_col).count()
    large_sessions = session_sizes.filter(col("count") > 1000).select(session_col)

    # 分离大session和小session
    df_large = df.join(large_sessions, session_col, "inner")
    df_small = df.join(large_sessions, session_col, "left_anti")

    print(f"大session数据量: {df_large.count()}")
    print(f"小session数据量: {df_small.count()}")

    return df_large, df_small


def process_data_with_pyspark(spark, input_path, output_json_path, args):
    """使用PySpark处理数据，确保同一session的数据在同一分区"""
    print(f"开始使用PySpark处理数据...")
    start_time = time.time()

    # 读取数据 - 支持多种格式
    if input_path.endswith('.parquet'):
        df = spark.read.parquet(input_path)
    elif input_path.endswith('.csv'):
        df = spark.read.option("header", "true").csv(input_path)
    elif input_path.endswith('.json'):
        df = spark.read.json(input_path)
    else:
        # 假设是pickle文件，需要先转换
        print("警告: PySpark不直接支持pickle文件，请先转换为parquet/csv/json格式")
        return

    print(f"数据总行数: {df.count()}")
    print("数据schema:")
    df.printSchema()

    # 注册UDF函数
    get_session_id_udf = udf(get_session_id, StringType())
    get_timestamp_udf = udf(get_timestamp, IntegerType())

    # 提取session_id和timestamp
    df_with_session = df.withColumn("short_sessionid_", get_session_id_udf(col("sessionid_"))) \
                        .withColumn("timestamp_", get_timestamp_udf(col("sessionid_")))

    # 过滤掉空的session_id
    df_filtered = df_with_session.filter(col("short_sessionid_").isNotNull())

    # 关键改进：按session_id重新分区，确保同一session的数据在同一分区
    print("按session_id重新分区...")
    # 计算合适的分区数：基于数据量和session数量
    total_sessions = df_filtered.select("short_sessionid_").distinct().count()
    optimal_partitions = min(max(total_sessions // 1000, 10), 200)  # 每个分区约1000个session
    print(f"使用 {optimal_partitions} 个分区进行重新分区")

    df_partitioned = df_filtered.repartition(optimal_partitions, col("short_sessionid_"))

    # 缓存分区后的数据，避免重复计算
    df_partitioned.cache()

    # 按session_id分组并收集所有记录
    print("按session_id分组...")
    df_grouped = df_partitioned.groupBy("short_sessionid_") \
                              .agg(collect_list("*").alias("records"),
                                   count("*").alias("record_count"))

    # 验证分组正确性
    validate_session_grouping(df_filtered, df_grouped)

    # 过滤轨迹长度
    df_length_filtered = df_grouped.filter(
        (col("record_count") >= args.trajectory_length_min) &
        (col("record_count") <= args.trajectory_length_max)
    )

    print(f"过滤后的session数量: {df_length_filtered.count()}")
    
    # 收集结果并写入文件
    print("收集结果并写入文件...")
    results = df_length_filtered.collect()
    
    length_dist = Counter()
    written_sessions = 0
    
    with open(output_json_path, 'w', encoding='utf-8') as fout:
        for row in results:
            session_id = row['short_sessionid_']
            records = row['records']
            record_count = row['record_count']
            
            length_dist[record_count] += 1
            
            # 转换记录为JSON可序列化格式
            serializable_records = []
            for record in records:
                # 将Row对象转换为字典
                record_dict = record.asDict()
                serializable_record = convert_to_json_serializable(record_dict)
                serializable_records.append(serializable_record)
            
            # 按timestamp排序
            serializable_records.sort(key=lambda x: x.get('timestamp_', 0))
            
            # 写入文件
            serialized = json.dumps(serializable_records, ensure_ascii=False, separators=(',', ':'))
            fout.write(f"{session_id}\t*#&\t{serialized}\n")
            written_sessions += 1
            
            if written_sessions % 1000 == 0:
                print(f"已写入 {written_sessions} 个sessions")
    
    end_time = time.time()
    print(f"PySpark处理完成，耗时: {end_time - start_time:.2f}秒")
    print(f"写入了 {written_sessions} 个有效sessions")
    
    # 输出长度分布
    print("Session长度分布（前20个）:")
    sorted_lengths = sorted(length_dist.items(), key=lambda x: x[1], reverse=True)[:20]
    for length, count in sorted_lengths:
        print(f"  长度 {length}: {count} 个sessions")
    
    return written_sessions


def convert_pickle_to_parquet(spark, pickle_dir, parquet_dir, target_pickles):
    """将pickle文件转换为parquet格式（需要pandas支持）"""
    import pandas as pd
    
    if not os.path.exists(parquet_dir):
        os.makedirs(parquet_dir)
    
    pickle_files = []
    if target_pickles == "all":
        pickle_files = [f for f in os.listdir(pickle_dir) if f.endswith("pickle")]
    else:
        pickle_files = target_pickles.strip().split(",")
    
    for pickle_file in pickle_files:
        pickle_path = os.path.join(pickle_dir, pickle_file)
        parquet_path = os.path.join(parquet_dir, pickle_file.replace('.pickle', '.parquet'))
        
        print(f"转换 {pickle_file} 到 parquet 格式...")
        
        # 使用pandas读取pickle，然后转换为Spark DataFrame
        pandas_df = pd.read_pickle(pickle_path)
        spark_df = spark.createDataFrame(pandas_df)
        
        # 写入parquet
        spark_df.write.mode("overwrite").parquet(parquet_path)
        print(f"已保存到: {parquet_path}")


def group_by_session_id_pyspark(args):
    """主处理函数"""
    spark = create_spark_session("GroupBySessionIdPySpark")
    
    try:
        # 如果输入是pickle目录，先转换为parquet
        if args.input_pickle_dir and os.path.isdir(args.input_pickle_dir):
            parquet_dir = args.input_pickle_dir + "_parquet"
            print(f"转换pickle文件到parquet格式: {parquet_dir}")
            convert_pickle_to_parquet(spark, args.input_pickle_dir, parquet_dir, args.target_pickles)
            input_path = parquet_dir
        else:
            input_path = args.input_path
        
        # 清空输出文件
        if os.path.exists(args.output_json_path):
            os.remove(args.output_json_path)
        
        # 处理数据
        if os.path.isdir(input_path):
            # 处理目录中的所有parquet文件
            parquet_files = [f for f in os.listdir(input_path) if f.endswith('.parquet')]
            total_written = 0
            
            for parquet_file in parquet_files:
                file_path = os.path.join(input_path, parquet_file)
                print(f"\n处理文件: {parquet_file}")
                written = process_data_with_pyspark(spark, file_path, args.output_json_path, args)
                total_written += written
            
            print(f"\n总共写入 {total_written} 个sessions")
        else:
            # 处理单个文件
            process_data_with_pyspark(spark, input_path, args.output_json_path, args)
            
    finally:
        spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="PySpark版本的数据按session分组处理")
    parser.add_argument("--input_pickle_dir", type=str, help="输入pickle文件目录", default="")
    parser.add_argument("--input_path", type=str, help="输入数据路径(parquet/csv/json)", default="")
    parser.add_argument("--target_pickles", type=str, help="目标pickle文件: all|file1,file2,file3", default="all")
    
    parser.add_argument("--trajectory_length_min", type=int, help="轨迹最小长度", default=1)
    parser.add_argument("--trajectory_length_max", type=int, help="轨迹最大长度", default=20)
    parser.add_argument("--output_json_path", type=str, help="输出JSON文件路径", default="")
    
    print("PySpark版本启动...")
    args = parser.parse_args()
    print("参数:")
    print(args)
    
    if not args.input_pickle_dir and not args.input_path:
        print("错误: 必须指定 --input_pickle_dir 或 --input_path")
        exit(1)
    
    if not args.output_json_path:
        print("错误: 必须指定 --output_json_path")
        exit(1)
    
    group_by_session_id_pyspark(args)
