# 优化版调度器使用说明

## 主要改进

1. **非阻塞任务执行**：任务在后台异步执行，不会阻塞调度器
2. **任务超时管理**：自动终止超时任务（默认12小时）
3. **并发控制**：支持同时运行多个任务（默认最多2个）
4. **实时日志查看**：支持查看正在运行任务的日志
5. **任务状态跟踪**：完整的任务生命周期管理

## 基本用法

### 1. 启动单次任务（异步模式）
```bash
python scheduler_day_allapp_optimized.py --run_once --session_cgi_label cgi7
```

### 2. 启动单次任务（实时日志模式）
```bash
python scheduler_day_allapp_optimized.py --run_once --session_cgi_label cgi7 --realtime_log
```

### 3. 启动定时任务（异步模式）
```bash
python scheduler_day_allapp_optimized.py --start_time 00:00 --end_time 00:10 --session_cgi_label cgi7
```

### 4. 查看调度器状态
```bash
python scheduler_day_allapp_optimized.py --status
```

### 5. 查看任务日志
```bash
# 查看最近50行日志
python scheduler_day_allapp_optimized.py --log cgi7_20231215

# 实时跟踪日志（类似 tail -f）
python scheduler_day_allapp_optimized.py --tail cgi7_20231215
```

## 高级配置

### 并发和超时设置
```bash
python scheduler_day_allapp_optimized.py \
  --max_concurrent 3 \
  --task_timeout 14400 \
  --session_cgi_label cgi7
```

### 多进程优化
```bash
python scheduler_day_allapp_optimized.py \
  --run_once \
  --use_multiprocess \
  --max_workers 4 \
  --chunk_size 2000 \
  --session_cgi_label cgi7
```

## 日志功能详解

### 1. 日志文件位置
- 所有任务日志保存在 `logs/` 目录下
- 文件名格式：`{task_id}.log`
- 例如：`logs/cgi7_20231215.log`

### 2. 实时日志模式
使用 `--realtime_log` 参数时：
- 任务输出会实时显示在终端
- 同时保存到日志文件
- 输出格式：`[时间][任务ID] 原始输出`

### 3. 日志查看命令
```bash
# 查看任务日志（默认最后50行）
python scheduler_day_allapp_optimized.py --log cgi7_20231215

# 实时跟踪正在运行的任务日志
python scheduler_day_allapp_optimized.py --tail cgi7_20231215
```

## 任务状态管理

### 任务状态类型
- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 成功完成
- `failed`: 执行失败
- `timeout`: 执行超时
- `cancelled`: 被取消

### 状态持久化
- 任务状态保存在 `scheduler_state.json`
- 调度器重启后会恢复状态信息
- 支持查看历史任务记录

## 故障处理

### 1. 任务超时
- 默认超时时间：12小时
- 超时任务会被自动终止
- 可通过 `--task_timeout` 调整

### 2. 并发限制
- 默认最大并发：2个任务
- 达到限制时新任务会被拒绝
- 可通过 `--max_concurrent` 调整

### 3. 优雅关闭
- 使用 Ctrl+C 优雅关闭调度器
- 会等待正在运行的任务完成或超时

## 监控和调试

### 1. 实时监控
```bash
# 每10分钟自动显示状态
python scheduler_day_allapp_optimized.py --start_time 00:00 --end_time 23:59

# 手动查看状态
python scheduler_day_allapp_optimized.py --status
```

### 2. 日志分析
```bash
# 查看调度器日志
tail -f scheduler.log

# 查看特定任务日志
tail -f logs/cgi7_20231215.log
```

## 兼容性说明

### 同步模式（兼容旧版本）
```bash
# 使用同步模式（阻塞执行）
python scheduler_day_allapp_optimized.py --run_once --sync_mode --session_cgi_label cgi7
```

### 原有函数接口
- `run_task_optimized()`: 现在默认使用异步模式
- `run_task_sync()`: 新增的同步模式函数
- `run_task()`: 保持兼容性的原始接口

## 最佳实践

1. **生产环境**：使用异步模式，设置合理的并发数和超时时间
2. **调试阶段**：使用实时日志模式，便于观察执行过程
3. **监控运维**：定期检查 `--status` 和日志文件
4. **资源管理**：根据服务器性能调整 `--max_workers` 和 `--chunk_size`
