import argparse
import os
import subprocess
import time
import multiprocessing as mp
import threading
import signal
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from enum import Enum
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskInfo:
    """任务信息数据类"""
    task_id: str
    session_cgi_label: str
    start_time: datetime
    end_time: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    process: Optional[subprocess.Popen] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

    log_file: Optional[str] = None  # 日志文件路径

    def to_dict(self):
        """转换为字典，用于序列化"""
        return {
            'task_id': self.task_id,
            'session_cgi_label': self.session_cgi_label,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status.value,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'log_file': self.log_file
            # 注意：不包含 process 对象，因为它不能被序列化
        }

class TaskScheduler:
    """优化的任务调度器"""

    def __init__(self, max_concurrent_tasks: int = 2):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.running_tasks: Dict[str, TaskInfo] = {}
        self.completed_tasks: List[TaskInfo] = []
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)

    def _generate_task_id(self, session_cgi_label: str, date: datetime) -> str:
        """生成任务ID"""
        return f"{session_cgi_label}_{date.strftime('%Y%m%d')}"

    def _save_task_state(self):
        """保存任务状态到文件"""
        try:
            # 保存正在运行任务的基本信息（不包含进程对象）和已完成的任务
            running_tasks_info = []
            for task_id, task_info in self.running_tasks.items():
                running_tasks_info.append({
                    'task_id': task_info.task_id,
                    'session_cgi_label': task_info.session_cgi_label,
                    'start_time': task_info.start_time.isoformat(),
                    'status': task_info.status.value,
                    'log_file': task_info.log_file,
                    'script_path': f"temp_task_{task_id}.py"  # 记录脚本路径用于清理
                })

            state = {
                'running_tasks': running_tasks_info,
                'completed_tasks': [task.to_dict() for task in self.completed_tasks[-100:]]  # 只保存最近100个
            }
            with open('scheduler_state.json', 'w') as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存任务状态失败: {e}")

    def _load_task_state(self):
        """从文件加载任务状态"""
        try:
            if os.path.exists('scheduler_state.json'):
                with open('scheduler_state.json', 'r') as f:
                    state = json.load(f)

                    # 处理之前运行的任务（调度器重启后这些任务已经丢失）
                    previous_running_tasks = state.get('running_tasks', [])
                    if previous_running_tasks:
                        self.logger.info(f"发现 {len(previous_running_tasks)} 个之前运行的任务，正在清理...")
                        for task_data in previous_running_tasks:
                            # 清理临时脚本文件
                            script_path = task_data.get('script_path')
                            if script_path and os.path.exists(script_path):
                                try:
                                    os.remove(script_path)
                                    self.logger.info(f"清理了临时脚本: {script_path}")
                                except Exception as e:
                                    self.logger.error(f"清理临时脚本失败 {script_path}: {e}")

                            # 将这些任务标记为中断（因为调度器重启）
                            task_info = TaskInfo(
                                task_id=task_data['task_id'],
                                session_cgi_label=task_data['session_cgi_label'],
                                start_time=datetime.fromisoformat(task_data['start_time']),
                                end_time=datetime.now(),
                                status=TaskStatus.CANCELLED,
                                error_message="调度器重启，任务中断",
                                log_file=task_data.get('log_file')
                            )
                            self.completed_tasks.append(task_info)

                    # 恢复已完成的任务（仅用于统计）
                    for task_data in state.get('completed_tasks', []):
                        task_info = TaskInfo(
                            task_id=task_data['task_id'],
                            session_cgi_label=task_data['session_cgi_label'],
                            start_time=datetime.fromisoformat(task_data['start_time']),
                            end_time=datetime.fromisoformat(task_data['end_time']) if task_data['end_time'] else None,
                            status=TaskStatus(task_data['status']),
                            error_message=task_data.get('error_message'),
                            retry_count=task_data.get('retry_count', 0),
                            max_retries=task_data.get('max_retries', 3),
                            log_file=task_data.get('log_file')
                        )
                        self.completed_tasks.append(task_info)

                    total_loaded = len(self.completed_tasks)
                    self.logger.info(f"加载了 {total_loaded} 个历史任务记录")

                    # 保存更新后的状态（移除之前运行的任务）
                    self._save_task_state()

        except Exception as e:
            self.logger.error(f"加载任务状态失败: {e}")

    def _cleanup_finished_tasks(self):
        """清理已完成的任务"""
        with self.lock:
            finished_tasks = []
            for task_id, task_info in list(self.running_tasks.items()):
                if task_info.process and task_info.process.poll() is not None:
                    # 进程已结束
                    task_info.end_time = datetime.now()
                    if task_info.process.returncode == 0:
                        task_info.status = TaskStatus.COMPLETED
                        self.logger.info(f"任务 {task_id} 成功完成")
                    else:
                        task_info.status = TaskStatus.FAILED
                        task_info.error_message = f"进程退出码: {task_info.process.returncode}"
                        self.logger.error(f"任务 {task_id} 执行失败: {task_info.error_message}")

                    # 清理临时脚本文件
                    self._cleanup_temp_script(task_id)

                    finished_tasks.append(task_info)
                    del self.running_tasks[task_id]



            self.completed_tasks.extend(finished_tasks)
            if finished_tasks:
                self._save_task_state()

    def _terminate_task(self, task_info: TaskInfo):
        """终止任务"""
        try:
            if task_info.process and task_info.process.poll() is None:
                # 首先尝试优雅终止
                task_info.process.terminate()
                time.sleep(5)

                # 如果还没结束，强制杀死
                if task_info.process.poll() is None:
                    task_info.process.kill()
                    task_info.process.wait()

                task_info.status = TaskStatus.CANCELLED
                task_info.end_time = datetime.now()
                task_info.error_message = "任务被手动终止"
                self.logger.warning(f"任务 {task_info.task_id} 已被终止")
        except Exception as e:
            self.logger.error(f"终止任务 {task_info.task_id} 时出错: {e}")

    def _cleanup_temp_script(self, task_id: str):
        """清理临时脚本文件"""
        try:
            script_path = f"temp_task_{task_id}.py"
            if os.path.exists(script_path):
                os.remove(script_path)
                self.logger.info(f"清理临时脚本: {script_path}")
        except Exception as e:
            self.logger.error(f"清理临时脚本失败: {e}")

    def _cleanup_orphaned_scripts(self):
        """清理所有孤儿临时脚本文件"""
        try:
            import glob
            script_pattern = "temp_task_*.py"
            orphaned_scripts = glob.glob(script_pattern)

            if orphaned_scripts:
                self.logger.info(f"发现 {len(orphaned_scripts)} 个孤儿脚本文件，正在清理...")
                for script_path in orphaned_scripts:
                    try:
                        os.remove(script_path)
                        self.logger.info(f"清理了孤儿脚本: {script_path}")
                    except Exception as e:
                        self.logger.error(f"清理孤儿脚本失败 {script_path}: {e}")
        except Exception as e:
            self.logger.error(f"清理孤儿脚本时出错: {e}")

    def can_start_new_task(self) -> bool:
        """检查是否可以启动新任务"""
        self._cleanup_finished_tasks()
        return len(self.running_tasks) < self.max_concurrent_tasks

    def is_task_running(self, task_id: str) -> bool:
        """检查指定任务是否正在运行"""
        self._cleanup_finished_tasks()
        return task_id in self.running_tasks

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id].status

        for task in self.completed_tasks:
            if task.task_id == task_id:
                return task.status

        return None

    def start_task_async(self, session_cgi_label: str, use_multiprocess: bool = True,
                        max_workers: int = None, chunk_size: int = 1000) -> Optional[str]:
        """异步启动任务"""
        yesterday = datetime.now() - timedelta(days=1)
        task_id = self._generate_task_id(session_cgi_label, yesterday)

        # 检查是否可以启动新任务
        if not self.can_start_new_task():
            self.logger.warning(f"无法启动任务 {task_id}：已达到最大并发任务数 ({self.max_concurrent_tasks})")
            return None

        # 检查是否已有相同任务在运行
        if self.is_task_running(task_id):
            self.logger.warning(f"任务 {task_id} 已在运行中")
            return None

        # 检查今天是否已经成功完成过这个任务
        existing_status = self.get_task_status(task_id)
        if existing_status == TaskStatus.COMPLETED:
            self.logger.info(f"任务 {task_id} 今天已经成功完成过了")
            return None

        try:
            task_info = TaskInfo(
                task_id=task_id,
                session_cgi_label=session_cgi_label,
                start_time=datetime.now()
            )

            # 启动任务进程
            success = self._execute_task_process(task_info, use_multiprocess, max_workers, chunk_size)

            if success:
                with self.lock:
                    self.running_tasks[task_id] = task_info
                    self._save_task_state()

                self.logger.info(f"任务 {task_id} 已启动")
                return task_id
            else:
                return None

        except Exception as e:
            self.logger.error(f"启动任务 {task_id} 时出错: {e}")
            return None

    def get_task_log(self, task_id: str, lines: int = 50) -> Optional[str]:
        """获取任务日志"""
        task_info = self.running_tasks.get(task_id)
        if not task_info:
            # 检查已完成的任务
            for task in self.completed_tasks:
                if task.task_id == task_id:
                    task_info = task
                    break

        if not task_info or not task_info.log_file:
            return None

        try:
            if os.path.exists(task_info.log_file):
                with open(task_info.log_file, 'r') as f:
                    all_lines = f.readlines()
                    return ''.join(all_lines[-lines:])
            else:
                return "日志文件不存在"
        except Exception as e:
            return f"读取日志文件时出错: {e}"

    def tail_task_log(self, task_id: str, follow: bool = False):
        """实时查看任务日志（类似 tail -f）"""
        task_info = self.running_tasks.get(task_id)
        if not task_info or not task_info.log_file:
            print(f"任务 {task_id} 不存在或没有日志文件")
            return

        try:
            print(f"正在查看任务 {task_id} 的日志文件: {task_info.log_file}")
            print("按 Ctrl+C 退出")
            print("-" * 50)

            if not os.path.exists(task_info.log_file):
                print("等待日志文件创建...")
                while not os.path.exists(task_info.log_file) and task_info.status == TaskStatus.RUNNING:
                    time.sleep(1)

            with open(task_info.log_file, 'r') as f:
                # 先显示现有内容
                f.seek(0, 2)  # 移到文件末尾
                file_size = f.tell()
                f.seek(max(0, file_size - 2000))  # 显示最后2000字符
                print(f.read())

                if follow:
                    # 实时跟踪新内容
                    while task_info.status == TaskStatus.RUNNING:
                        line = f.readline()
                        if line:
                            print(line.rstrip())
                        else:
                            time.sleep(0.1)

        except KeyboardInterrupt:
            print("\n停止查看日志")
        except Exception as e:
            print(f"查看日志时出错: {e}")

    def _execute_task_process(self, task_info: TaskInfo, use_multiprocess: bool,
                             max_workers: int, chunk_size: int) -> bool:
        """执行完整的任务流程"""
        try:
            # 创建日志文件
            log_dir = "./logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            task_info.log_file = os.path.join(log_dir, f"{task_info.task_id}.log")

            # 创建完整任务执行脚本
            script_content = self._generate_complete_task_script(
                task_info.session_cgi_label, use_multiprocess, max_workers, chunk_size
            )

            # 保存脚本到临时文件
            script_path = f"temp_task_{task_info.task_id}.py"
            with open(script_path, 'w') as f:
                f.write(script_content)

            self.logger.info(f"启动完整任务 {task_info.task_id}")
            self.logger.info(f"任务日志文件: {task_info.log_file}")

            # 执行完整任务脚本
            log_file_handle = open(task_info.log_file, 'w')
            task_info.process = subprocess.Popen(
                ["python", script_path],
                stdout=log_file_handle,
                stderr=subprocess.STDOUT,
                text=True
            )

            task_info.status = TaskStatus.RUNNING
            return True

        except Exception as e:
            self.logger.error(f"执行任务 {task_info.task_id} 时出错: {e}")
            task_info.status = TaskStatus.FAILED
            task_info.error_message = str(e)
            return False

    def _generate_complete_task_script(self, session_cgi_label: str, use_multiprocess: bool,
                                     max_workers: int, chunk_size: int) -> str:
        """生成完整任务执行脚本"""
        # 使用字符串拼接而不是复杂的格式化，避免冲突
        script_lines = [
            "#!/usr/bin/env python3",
            "import os",
            "import subprocess",
            "import sys",
            "from datetime import datetime, timedelta",
            "",
            "def log_message(message):",
            '    """记录日志消息"""',
            "    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')",
            "    print(f'[{timestamp}] {message}')",
            "",
            "def main():",
            "    try:",
            "        # Step 1: 根据时间规则生成文件名",
            '        log_message("Step 1: Determining the generated file name...")',
            "        yesterday = datetime.now() - timedelta(days=1)",
            "        start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)",
            "        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)",
            "",
            "        start_str = start_time.strftime('%Y%m%d%H')",
            "        end_str = end_time.strftime('%Y%m%d%H')",
            "",
            f'        generated_file = f"data_{{start_str}}_{{end_str}}_all_app_{session_cgi_label}_new.pickle"',
            '        log_message(f"Generated file name: {generated_file}")',
            "",
            "        # Step 2: 执行 xml_decode_b_understand_all_app.py",
            '        log_message("Step 2: Running xml_decode_b_understand_all_app.py...")',
            "",
            "        cmd_args = [",
            '            "python", "xml_decode_b_understand_all_app.py",',
            '            f"--output_file_name={generated_file}",',
            '            f"--start_time={start_time.strftime(\'%Y-%m-%d %H:%M:%S\')}",',
            '            f"--end_time={end_time.strftime(\'%Y-%m-%d %H:%M:%S\')}",',
            f'            "--session_cgi_label={session_cgi_label}",',
            f'            "--use_multiprocess={use_multiprocess}",',
            f'            "--chunk_size={chunk_size}"',
            "        ]",
            ""
        ]

        # 添加 max_workers 参数（如果有）
        if max_workers:
            script_lines.append(f'        cmd_args.append("--max_workers={max_workers}")')
        else:
            script_lines.append("        # No max_workers specified")

        script_lines.extend([
            "",
            '        log_message(f"执行命令: {\' \'.join(cmd_args)}")',
            "        result = subprocess.run(cmd_args, check=True, capture_output=True, text=True)",
            '        log_message("Step 2 completed successfully.")',
            "        if result.stdout:",
            "            print(result.stdout)",
            "",
            "        # 检查文件是否存在",
            "        if not os.path.exists(generated_file):",
            '            raise FileNotFoundError(f"Generated file \'{generated_file}\' not found!")',
            "",
            "        # Step 2.5: 将生成的文件复制到目标目录",
            '        log_message("Step 2.5: Copying file to destination...")',
            "        subprocess.run(",
            '            ["cp", generated_file, "/mnt/cephfs/user_claireysun/data/xcx_pickle/"],',
            "            check=True",
            "        )",
            '        log_message("Step 2.5 completed successfully.")',
            "",
            "        # Step 3: 执行 group_by_session_id.py",
            '        log_message(f"Step 3: Running group_by_session_id.py with input file \'{generated_file}\'...")',
            '        output_json_path = f"{generated_file.split(\'.\')[0]}_groupby_session.json"',
            "",
            "        result = subprocess.run([",
            '            "python", "group_by_session_id.py",',
            '            "--function=group_by_session_id",',
            '            "--input_pickle_dir=./",',
            '            f"--target_pickles={generated_file}",',
            '            "--trajectory_length_min=1",',
            '            "--trajectory_length_max=20",',
            '            f"--output_json_path={output_json_path}"',
            "        ], check=True, capture_output=True, text=True)",
            "",
            '        log_message("Step 3 completed successfully.")',
            "        if result.stdout:",
            "            print(result.stdout)",
            "",
            "        # Step 4: 将 output_json_path 文件复制到目标目录",
            '        log_message(f"Step 4: Copying \'{output_json_path}\' to session directory...")',
            "        if not os.path.exists(output_json_path):",
            '            raise FileNotFoundError(f"Output JSON file \'{output_json_path}\' not found!")',
            "",
            "        subprocess.run(",
            '            ["cp", output_json_path, "/mnt/cephfs/user_claireysun/data/xcx_session/"],',
            "            check=True",
            "        )",
            '        log_message("Step 4 completed successfully.")',
            "",
            "        # Step 5: 删除生成的文件",
            '        log_message("Step 5: Cleaning up generated files...")',
            "        if os.path.exists(generated_file):",
            "            os.remove(generated_file)",
            '            log_message(f"Deleted file: {generated_file}")',
            "        if os.path.exists(output_json_path):",
            "            os.remove(output_json_path)",
            '            log_message(f"Deleted file: {output_json_path}")',
            "",
            '        log_message("任务完成！")',
            "",
            "    except subprocess.CalledProcessError as e:",
            '        log_message(f"Error occurred while running a subprocess:")',
            '        log_message(f"Return code: {e.returncode}")',
            "        if e.stdout:",
            '            log_message(f"STDOUT: {e.stdout}")',
            "        if e.stderr:",
            '            log_message(f"STDERR: {e.stderr}")',
            "        sys.exit(1)",
            "    except FileNotFoundError as e:",
            '        log_message(f"Error: {e}")',
            "        sys.exit(1)",
            "    except Exception as e:",
            '        log_message(f"Unexpected error: {e}")',
            "        sys.exit(1)",
            "",
            'if __name__ == "__main__":',
            "    main()"
        ])

        return "\n".join(script_lines)



    def get_running_tasks_info(self) -> List[Dict]:
        """获取正在运行的任务信息"""
        self._cleanup_finished_tasks()
        return [task.to_dict() for task in self.running_tasks.values()]

    def get_completed_tasks_info(self, limit: int = 10) -> List[Dict]:
        """获取已完成任务信息"""
        return [task.to_dict() for task in self.completed_tasks[-limit:]]

    def stop_all_tasks(self):
        """停止所有正在运行的任务"""
        with self.lock:
            for task_info in self.running_tasks.values():
                if task_info.process and task_info.process.poll() is None:
                    self.logger.info(f"正在停止任务 {task_info.task_id}")
                    self._terminate_task(task_info)

                # 清理临时脚本文件
                self._cleanup_temp_script(task_info.task_id)

            self.running_tasks.clear()
            self._save_task_state()

# 全局调度器实例
scheduler = TaskScheduler()

def run_task_optimized(session_cgi_label: str, use_multiprocess: bool = True, max_workers: int = None,
                      chunk_size: int = 1000):
    """优化版本的任务执行函数 - 使用新的异步调度器"""
    task_id = scheduler.start_task_async(session_cgi_label, use_multiprocess, max_workers, chunk_size)
    if task_id:
        print(f"[{datetime.now()}] 任务 {task_id} 已启动（异步执行）")
        print(f"[{datetime.now()}] 使用 'python {__file__} --log {task_id}' 查看日志")
        return task_id
    else:
        print(f"[{datetime.now()}] 无法启动任务 {session_cgi_label}（可能已在运行或达到并发限制）")
        return None

def run_task_sync(session_cgi_label: str, use_multiprocess: bool = True, max_workers: int = None, chunk_size: int = 1000):
    """同步版本的任务执行函数（保留原有逻辑用于兼容性）"""
    try:
        # Step 1: 根据时间规则生成文件名
        print(f"[{datetime.now()}] Step 1: Determining the generated file name...")
        yesterday = datetime.now() - timedelta(days=1)
        start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

        start_str = start_time.strftime('%Y%m%d%H')  # 格式化为 YYYYMMDDHH
        end_str = end_time.strftime('%Y%m%d%H')      # 格式化为 YYYYMMDDHH

        # 动态生成文件名
        generated_file = f"data_{start_str}_{end_str}_all_app_{session_cgi_label}_new.pickle"
        print(f"[{datetime.now()}] To be Generated file name: {generated_file}")

        # Step 2: 执行优化版本的 xml_decode_b_understand_all_app.py
        print(f"[{datetime.now()}] Step 2: Running optimized xml_decode_b_understand_all_app.py...")

        # 构建命令参数
        cmd_args = [
            "python", "xml_decode_b_understand_all_app.py",
            f"--output_file_name={generated_file}",
            f"--start_time={start_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"--end_time={end_time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"--session_cgi_label={session_cgi_label}",
            f"--use_multiprocess={use_multiprocess}",
            f"--chunk_size={chunk_size}"
        ]

        if max_workers:
            cmd_args.append(f"--max_workers={max_workers}")

        print(f"[{datetime.now()}] 执行命令: {' '.join(cmd_args)}")

        result_step1 = subprocess.run(
            cmd_args,
            check=True,
            capture_output=True,
            text=True
        )
        print(f"[{datetime.now()}] Step 2 completed successfully.")
        print(result_step1.stdout)

        # 检查文件是否存在
        if not os.path.exists(generated_file):
            raise FileNotFoundError(f"Generated file '{generated_file}' not found!")

        # Step 2.5: 将生成的文件复制到目标目录
        subprocess.run(
            ["cp", generated_file, "/mnt/cephfs/user_claireysun/data/xcx_pickle/"],
            check=True
        )
        print(f"[{datetime.now()}] Step 2.5 completed successfully. File copied to destination.")

        # Step 3: 执行 group_by_session_id.py
        print(f"[{datetime.now()}] Step 3: Running group_by_session_id.py with input file '{generated_file}'...")
        output_json_path = f"{generated_file.split('.')[0]}_groupby_session.json"
        result_step3 = subprocess.run(
            [
                "python", "group_by_session_id.py",
                "--function=group_by_session_id",
                "--input_pickle_dir=./",
                f"--target_pickles={generated_file}",
                "--trajectory_length_min=1",
                "--trajectory_length_max=20",
                f"--output_json_path={output_json_path}"
            ],
            check=True,
            capture_output=True,
            text=True
        )
        print(f"[{datetime.now()}] Step 3 completed successfully.")
        print(result_step3.stdout)

        # Step 4: 将 output_json_path 文件复制到 /mnt/ce
        print(f"[{datetime.now()}] Step 4: Copying '{output_json_path}' to '/mnt/cephfs/user_claireysun/data/xcx_session/'...")
        if not os.path.exists(output_json_path):
            raise FileNotFoundError(f"Output JSON file '{output_json_path}' not found!")
        subprocess.run(
            ["cp", output_json_path, "/mnt/cephfs/user_claireysun/data/xcx_session/"],
            check=True
        )
        print(f"[{datetime.now()}] Step 4 completed successfully. File copied to destination.")

        # Step 5: 删除生成的文件
        print(f"[{datetime.now()}] Step 5: Cleaning up generated files...")
        if os.path.exists(generated_file):
            os.remove(generated_file)
            print(f"[{datetime.now()}] Deleted file: {generated_file}")
        if os.path.exists(output_json_path):
            os.remove(output_json_path)
            print(f"[{datetime.now()}] Deleted file: {output_json_path}")

        print(f"[{datetime.now()}] 任务完成！")

    except subprocess.CalledProcessError as e:
        print(f"[{datetime.now()}] Error occurred while running a subprocess:")
        print(f"Return code: {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
    except FileNotFoundError as e:
        print(f"[{datetime.now()}] Error: {e}")
    except Exception as e:
        print(f"[{datetime.now()}] Unexpected error: {e}")

def run_task(session_cgi_label: str):
    """原始版本的任务执行函数（保留兼容性）"""
    run_task_optimized(session_cgi_label, use_multiprocess=False)

def print_status():
    """打印调度器状态"""
    running_tasks = scheduler.get_running_tasks_info()
    completed_tasks = scheduler.get_completed_tasks_info(5)

    print(f"\n=== 调度器状态 [{datetime.now()}] ===")
    print(f"正在运行的任务数: {len(running_tasks)}")
    for task in running_tasks:
        elapsed = (datetime.now() - datetime.fromisoformat(task['start_time'])).total_seconds()
        print(f"  - {task['task_id']}: {task['status']} (运行时间: {elapsed:.0f}秒)")

    print(f"最近完成的任务:")
    for task in completed_tasks:
        print(f"  - {task['task_id']}: {task['status']}")
    print("=" * 50)

def signal_handler(signum, _):
    """信号处理器，用于优雅关闭"""
    print(f"\n收到信号 {signum}，正在关闭调度器...")
    scheduler.stop_all_tasks()
    print("调度器已关闭")
    exit(0)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="优化版定时任务脚本")
    parser.add_argument("--interval", type=int, default=60, help="定时任务间隔时间（秒）")
    parser.add_argument("--start_time", type=str, default="00:00", help="定时任务开始时间（格式：HH:MM）")
    parser.add_argument("--end_time", type=str, default="00:10", help="定时任务结束时间（格式：HH:MM）")
    parser.add_argument("--session_cgi_label", type=str, default="cgi7", help="会话 CGI 标签")
    parser.add_argument("--use_multiprocess", action="store_true", help="是否使用多进程加速")
    parser.add_argument("--max_workers", type=int, default=None, help="最大进程数")
    parser.add_argument("--chunk_size", type=int, default=1000, help="每个批次处理的行数")
    parser.add_argument("--run_once", action="store_true", help="只运行一次，不进入定时循环")
    parser.add_argument("--sync_mode", action="store_true", help="使用同步模式（阻塞执行）")
    parser.add_argument("--max_concurrent", type=int, default=2, help="最大并发任务数")
    parser.add_argument("--status", action="store_true", help="显示调度器状态")
    parser.add_argument("--log", type=str, help="查看指定任务的日志（任务ID）")
    parser.add_argument("--tail", type=str, help="实时跟踪指定任务的日志（任务ID）")

    args = parser.parse_args()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 配置调度器
    scheduler.max_concurrent_tasks = args.max_concurrent

    # 启动时清理孤儿脚本文件
    scheduler._cleanup_orphaned_scripts()

    # 加载任务状态
    scheduler._load_task_state()

    print("优化版调度器启动")
    print(f"配置参数:")
    print(f"  会话标签: {args.session_cgi_label}")
    print(f"  多进程模式: {args.use_multiprocess}")
    print(f"  最大进程数: {args.max_workers or '自动'}")
    print(f"  批次大小: {args.chunk_size}")
    print(f"  最大并发任务数: {args.max_concurrent}")
    print(f"  同步模式: {args.sync_mode}")
    print(f"  系统CPU核心数: {mp.cpu_count()}")

    if args.status:
        print_status()
        exit(0)

    if args.log:
        # 查看任务日志
        log_content = scheduler.get_task_log(args.log)
        if log_content:
            print(f"=== 任务 {args.log} 的日志 ===")
            print(log_content)
        else:
            print(f"任务 {args.log} 不存在或没有日志")
        exit(0)

    if args.tail:
        # 实时跟踪任务日志
        scheduler.tail_task_log(args.tail, follow=True)
        exit(0)

    if args.run_once:
        print("单次运行模式")
        if args.sync_mode:
            run_task_sync(
                args.session_cgi_label,
                args.use_multiprocess,
                args.max_workers,
                args.chunk_size
            )
        else:
            task_id = run_task_optimized(
                args.session_cgi_label,
                args.use_multiprocess,
                args.max_workers,
                args.chunk_size
            )
            if task_id:
                print(f"任务 {task_id} 已启动，使用 --status 参数查看状态")
    else:
        print(f"定时任务模式: {args.start_time} - {args.end_time}")
        print("使用 Ctrl+C 优雅关闭调度器")

        # 定时任务主循环
        last_status_time = 0
        try:
            while True:
                current_time = datetime.now().strftime("%H:%M")

                # 每10分钟打印一次状态
                if time.time() - last_status_time > 600:
                    print_status()
                    last_status_time = time.time()

                # 检查是否是目标时间范围
                if args.start_time <= current_time <= args.end_time:
                    if args.sync_mode:
                        run_task_sync(
                            args.session_cgi_label,
                            args.use_multiprocess,
                            args.max_workers,
                            args.chunk_size
                        )
                        time.sleep(3600)  # 等待一个小时，避免重复执行
                    else:
                        # 异步模式：尝试启动任务，如果已在运行则跳过
                        task_id = run_task_optimized(
                            args.session_cgi_label,
                            args.use_multiprocess,
                            args.max_workers,
                            args.chunk_size
                        )
                        if task_id:
                            print(f"任务 {task_id} 已启动")

                        # 异步模式下不需要长时间等待
                        time.sleep(args.interval)
                else:
                    # 每分钟检查一次时间
                    time.sleep(args.interval)

        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
